["\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\image\\autogenerated\\commands\\from_bytes.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\image\\autogenerated\\commands\\from_path.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\image\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\image\\autogenerated\\commands\\rgba.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\image\\autogenerated\\commands\\size.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\image\\autogenerated\\default.toml"]