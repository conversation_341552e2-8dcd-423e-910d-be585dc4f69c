["\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\bundle_type.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\D:\\github\\projects\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-22cd82d1103ffe0a\\out\\permissions\\app\\autogenerated\\default.toml"]