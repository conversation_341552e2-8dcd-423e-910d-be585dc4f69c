{"name": "screen-recording", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@vueuse/core": "^13.6.0", "element-plus": "^2.10.7", "pinia": "^3.0.3", "vue": "^3.5.13"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}