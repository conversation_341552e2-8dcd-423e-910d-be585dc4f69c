{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 7590839994313063594, "deps": [[376837177317575824, "softbuffer", false, 5014915658872495827], [654232091421095663, "tauri_utils", false, 10233542002902455454], [2013030631243296465, "webview2_com", false, 9053399379240280340], [3150220818285335163, "url", false, 6488912869113852674], [3722963349756955755, "once_cell", false, 9148332056717002387], [4143744114649553716, "raw_window_handle", false, 126447740291223225], [5986029879202738730, "log", false, 17103523495733528716], [8826339825490770380, "tao", false, 5855806149920502397], [9010263965687315507, "http", false, 17046921390355211077], [9141053277961803901, "wry", false, 9168544956344146505], [12304025191202589669, "build_script_build", false, 18195759084751001074], [12943761728066819757, "tauri_runtime", false, 4389897466724858857], [14585479307175734061, "windows", false, 11020930496771632489]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-8608f592676dbfe8\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}