{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8894278055034350775, "deps": [[654232091421095663, "tauri_utils", false, 9488190457862301970], [2995469292676432503, "uuid", false, 7732894400328568775], [3150220818285335163, "url", false, 1066038494707245096], [4899080583175475170, "semver", false, 14977641941781102605], [4974441333307933176, "syn", false, 16887633355443958115], [7170110829644101142, "json_patch", false, 3174200472440724309], [7392050791754369441, "ico", false, 17605265302326925008], [9556762810601084293, "brotli", false, 1612878063969801434], [9689903380558560274, "serde", false, 5584329322405189393], [9857275760291862238, "sha2", false, 4301780009647179487], [12687914511023397207, "png", false, 10576303033918697383], [12901820725121660946, "thiserror", false, 15988163678135775145], [13077212702700853852, "base64", false, 14787360010863829238], [13790829364578928694, "proc_macro2", false, 4746373800654736223], [15622660310229662834, "walkdir", false, 13141832595847327072], [16362055519698394275, "serde_json", false, 4468438538321398353], [17990358020177143287, "quote", false, 4241350359151612717]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-f45825281feb6319\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}