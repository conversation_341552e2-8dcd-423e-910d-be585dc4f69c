{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 6335087436104091831, "deps": [[40386456601120721, "percent_encoding", false, 11451824362267699693], [654232091421095663, "tauri_utils", false, 10233542002902455454], [1200537532907108615, "url<PERSON><PERSON>n", false, 15226184322921626779], [1967864351173319501, "muda", false, 12518446368874336717], [2013030631243296465, "webview2_com", false, 9053399379240280340], [3150220818285335163, "url", false, 6488912869113852674], [3331586631144870129, "getrandom", false, 4528896524885983854], [4143744114649553716, "raw_window_handle", false, 126447740291223225], [4919829919303820331, "serialize_to_javascript", false, 4258879344854279880], [5986029879202738730, "log", false, 17103523495733528716], [9010263965687315507, "http", false, 17046921390355211077], [9293239362693504808, "glob", false, 7268526671042401294], [9689903380558560274, "serde", false, 18180878095707125642], [10229185211513642314, "mime", false, 15459723414947441071], [11207653606310558077, "anyhow", false, 5190090078337922272], [11989259058781683633, "dunce", false, 11560442118751391268], [12092653563678505622, "build_script_build", false, 1048026800913612929], [12304025191202589669, "tauri_runtime_wry", false, 14767100779587842223], [12565293087094287914, "window_vibrancy", false, 10109244614685600910], [12901820725121660946, "thiserror", false, 2681874156562138581], [12943761728066819757, "tauri_runtime", false, 4389897466724858857], [12986574360607194341, "serde_repr", false, 12384289107830345716], [13077543566650298139, "heck", false, 977132307339587524], [13405681745520956630, "tauri_macros", false, 10417796715426018574], [14585479307175734061, "windows", false, 11020930496771632489], [16362055519698394275, "serde_json", false, 1406684971929634261], [16928111194414003569, "dirs", false, 12732796372557786993], [17531218394775549125, "tokio", false, 13004517618062702429]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-d4ed3d69f61fb3e7\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}