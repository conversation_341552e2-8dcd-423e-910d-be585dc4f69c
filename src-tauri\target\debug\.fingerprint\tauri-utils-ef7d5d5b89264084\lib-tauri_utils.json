{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 83139694899628915, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 15226184322921626779], [2995469292676432503, "uuid", false, 12768700465417183406], [3150220818285335163, "url", false, 6488912869113852674], [4071963112282141418, "serde_with", false, 16205179709728668197], [4899080583175475170, "semver", false, 16197991607841655478], [5986029879202738730, "log", false, 17103523495733528716], [6606131838865521726, "ctor", false, 945696418935883626], [7170110829644101142, "json_patch", false, 3894802310617722011], [9010263965687315507, "http", false, 17046921390355211077], [9090328626728818999, "toml", false, 14902028869260052060], [9293239362693504808, "glob", false, 7268526671042401294], [9451456094439810778, "regex", false, 8035670133280570470], [9556762810601084293, "brotli", false, 5646480653990378443], [9689903380558560274, "serde", false, 18180878095707125642], [11207653606310558077, "anyhow", false, 5190090078337922272], [11989259058781683633, "dunce", false, 11560442118751391268], [12901820725121660946, "thiserror", false, 2681874156562138581], [15622660310229662834, "walkdir", false, 11249427753159787704], [15932120279885307830, "memchr", false, 1811853779724236704], [16362055519698394275, "serde_json", false, 1406684971929634261], [17146114186171651583, "infer", false, 11664519839655875489], [17183029615630212089, "serde_untagged", false, 510851082421528231], [17186037756130803222, "phf", false, 419131652286345862]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-ef7d5d5b89264084\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}