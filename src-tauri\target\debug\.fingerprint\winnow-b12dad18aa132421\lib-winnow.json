{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 14113089254465536004, "profile": 2241668132362809309, "path": 5985862866066348957, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\winnow-b12dad18aa132421\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}