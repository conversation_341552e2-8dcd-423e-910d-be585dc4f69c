{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 4801497374888428737, "deps": [[654232091421095663, "tauri_utils", false, 9488190457862301970], [2704937418414716471, "tauri_codegen", false, 10730708047296535953], [4974441333307933176, "syn", false, 16887633355443958115], [13077543566650298139, "heck", false, 2310260232102032935], [13790829364578928694, "proc_macro2", false, 4746373800654736223], [17990358020177143287, "quote", false, 4241350359151612717]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-211d930ebf54139f\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}