{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 19569072800467306, "deps": [[654232091421095663, "tauri_utils", false, 9488190457862301970], [4824857623768494398, "cargo_toml", false, 7115833001380125265], [4899080583175475170, "semver", false, 14977641941781102605], [5165059047667588304, "tauri_winres", false, 889062776092299445], [6913375703034175521, "schemars", false, 15985076235971400192], [7170110829644101142, "json_patch", false, 3174200472440724309], [9090328626728818999, "toml", false, 17576864216212480443], [9293239362693504808, "glob", false, 15953679550155227386], [9689903380558560274, "serde", false, 5584329322405189393], [11207653606310558077, "anyhow", false, 12321044309432778963], [13077543566650298139, "heck", false, 2310260232102032935], [15622660310229662834, "walkdir", false, 13141832595847327072], [16362055519698394275, "serde_json", false, 4468438538321398353], [16928111194414003569, "dirs", false, 9629153185681952333]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-d8dc69f28b55c98f\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}