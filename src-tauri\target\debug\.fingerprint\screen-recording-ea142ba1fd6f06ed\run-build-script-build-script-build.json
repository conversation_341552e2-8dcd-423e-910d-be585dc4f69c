{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16687502426821174256, "build_script_build", false, 57364988095384021], [12092653563678505622, "build_script_build", false, 1048026800913612929], [2784153353110520258, "build_script_build", false, 7327335606687369169]], "local": [{"RerunIfChanged": {"output": "debug\\build\\screen-recording-ea142ba1fd6f06ed\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}