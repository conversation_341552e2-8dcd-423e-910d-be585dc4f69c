{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 2225463790103693989, "path": 15953272831349706717, "deps": [[6941104557053927479, "embed_resource", false, 13947753632122073352], [12060164242600251039, "toml", false, 6938311829602254906]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-a6ebf6df5954b58e\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}